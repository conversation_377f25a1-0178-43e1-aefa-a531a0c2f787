'use client';

import { UserMenuDropdownProps } from '@/components/molecules/UserMenuDropdown/UserMenuDropdown';
import Sidebar from '@/components/organisms/Sidebar/Sidebar';
import BottomNavigation from '@/components/organisms/BottomNavigation/BottomNavigation';
import Icon from '@/components/atoms/Icon/Icon';
import { cn } from '@/utils/cn';

import type { ComponentProps } from 'react';
import type { ISchoolResponse } from '@/apis/schoolApi'; // Import ISchoolResponse
import { usePathname, useRouter } from 'next/navigation';

export interface PageContainerProps extends ComponentProps<'div'> {
  pageContainerType?: 'default' | 'gutterless' | 'contained';
  sidebarItems: { label: string; href: string; icon?: React.ReactNode }[];
  children: React.ReactNode;
  userMenuDropdownProps: UserMenuDropdownProps;
  schoolInfo?: ISchoolResponse | null; // Changed to ISchoolResponse
}

const DashboardTemplate = ({
  pageContainerType = 'default',
  children,
  sidebarItems = [],
  userMenuDropdownProps,
  schoolInfo,
}: PageContainerProps) => {
  const isContained = pageContainerType === 'contained';
  const isAuth = sidebarItems.length > 0;
  const pathname = usePathname();
  const router = useRouter();

  const handleDrawerClose = () => {
    // Close drawer by unchecking the checkbox
    const drawerToggle = document.getElementById('dashboard-drawer') as HTMLInputElement;
    if (drawerToggle) {
      drawerToggle.checked = false;
    }
  };

  // Check if we should show back button
  const showBackButton = pathname?.includes('/manage-worksheet') && pathname?.includes('type=create');

  const handleBackClick = () => {
    router.push('/manage-worksheet');
  };

  // Convert sidebar items to bottom navigation format
  const bottomNavItems = sidebarItems.map(item => ({
    label: item.label,
    href: item.href,
    icon: item.icon || null
  }));

  if (!isAuth) {
    // For pages without sidebar (like auth pages), render without drawer
    return (
      <div className="min-h-screen">
        <main className="h-full">
          <div
            className={cn(
              'page-container flex-col flex h-full flex-auto relative bg-slate-50',
              pageContainerType !== 'gutterless' ? 'pt-6 pb-8.5 px-4' : '',
              isContained && 'mx-auto'
            )}
          >
            {isContained ? (
              <div className="container mx-auto">
                {children}
              </div>
            ) : (
              children
            )}
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="drawer lg:drawer-open">
      {/* Hidden checkbox to control drawer state */}
      <input
        id="dashboard-drawer"
        type="checkbox"
        className="drawer-toggle"
      />

      {/* Main content area */}
      <div className="drawer-content flex flex-col min-h-screen">
        {/* Mobile header with logo */}
        <div className="navbar bg-base-100 lg:hidden border-b border-base-300">
          <div className="flex-1 justify-center">
            <Icon variant="dashboard-icon" className="h-8 w-auto" size={62} />
          </div>
        </div>

        {/* Main content */}
        <main className="flex-1 pb-16 lg:pb-0">
          <div
            className={cn(
              'page-container flex-col flex h-full flex-auto relative bg-slate-50',
              pageContainerType !== 'gutterless' ? 'pt-6 pb-8.5 px-4' : '',
              isContained && 'mx-auto'
            )}
          >
            {isContained ? (
              <div className="container mx-auto">
                {children}
              </div>
            ) : (
              children
            )}
          </div>
        </main>

        {/* Bottom Navigation for mobile */}
        <BottomNavigation
          items={bottomNavItems}
        />
      </div>

      {/* Sidebar */}
      <div className="drawer-side border-r border-base-300">
        <label
          htmlFor="dashboard-drawer"
          className="drawer-overlay"
          onClick={handleDrawerClose}
        />
        <Sidebar
          items={sidebarItems}
          userMenuDropdownProps={userMenuDropdownProps}
          schoolInfo={schoolInfo}
          onClose={handleDrawerClose}
        />
      </div>
    </div>
  );
};

export default DashboardTemplate;
