'use client';

import React, { useState, useMemo, useCallback, useEffect } from 'react';
import {
  TWorksheet,
  WorksheetGeneratingStatus,
} from '@/apis/worksheet';
import { ERoutes } from '@/config/enums/enum';
import { getSocket } from '@/lib/socket';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';
import { ColumnDef } from '@tanstack/react-table';
import {
  Loader2,
  Edit,
  Trash2,
  MoreHorizontal,
  AlertCircle,
  Search,
  X,
  FileText,
  ChevronDown,
  ChevronUp,
  Download,
  Eye,
  Calendar,
  BookOpen,
  GraduationCap,
  Globe,
  ChevronRight,
} from 'lucide-react';

import { MobileOptimizedTable } from '@/components/molecules/CustomTable/MobileOptimizedTable';
import { MobileOptimizedTablePagination } from '../../TablePagination/MobileOptimizedTablePagination';
import Icon from '@/components/atoms/Icon';
import { Button } from '@/components/atoms/Button/Button';
import { AnimationStyles } from '@/components/atoms/AnimationStyles/AnimationStyles';
import { cn } from '@/utils/cn';

// Status Badge Component
const StatusBadge = ({ status }: { status: 'active' | 'inactive' | 'pending' | 'error' }) => {
  const statusConfig = {
    active: { color: 'bg-green-100 text-green-800', label: 'Generated' },
    inactive: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
    pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Generating...' },
    error: { color: 'bg-red-100 text-red-800', label: 'Error' },
  };
  
  const config = statusConfig[status];
  
  return (
    <span className={cn('px-2 py-1 rounded-full text-xs font-medium', config.color)}>
      {config.label}
    </span>
  );
};

// Delete Modal Placeholder
const DeleteWorksheetModal = ({ isOpen, onClose, worksheet, onSuccess }: any) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
      <div className="bg-black opacity-50 absolute inset-0 z-0"></div>
      <div className="bg-white rounded-lg p-6 max-w-md w-full relative z-10">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Worksheet</h3>
        <p className="text-gray-600 mb-6">
          Are you sure you want to delete &ldquo;{worksheet?.title}&rdquo;? This action cannot be undone.
        </p>
        <div className="flex gap-3 justify-end">
          <Button variant="ghost" onClick={onClose}>Cancel</Button>
          <Button variant="primary" className="bg-red-600 hover:bg-red-700" onClick={() => {
            onSuccess?.(worksheet?.id);
            onClose();
          }}>
            Delete
          </Button>
        </div>
      </div>
    </div>
  );
};

export interface MobileOptimizedWorksheetTableProps {
  worksheets: TWorksheet[];
  error: string | null;
  isLoading?: boolean;
  tableTitle?: string;
  entityName?: string;
  entityNamePlural?: string;
  createPath?: string;
  currentPageBackend?: number;
  totalPagesBackend?: number;
  totalItemsBackend?: number;
  onBackendPageChange?: (page: number) => void;
  onBackendRowsPerPageChange?: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  rowsPerPageBackend?: number;
}

// Helper to get nested properties safely
const getNestedValue = (obj: any, path: string, defaultValue: any = '') => {
  return path.split('.').reduce((acc, part) => acc && acc[part], obj) || defaultValue;
};

// Helper to get option values from the selectedOptions array
const getOptionValue = (selectedOptions: any, key: string) => {
  if (!selectedOptions || !Array.isArray(selectedOptions)) return '';
  const option = selectedOptions.find((opt: any) =>
    opt.optionType?.key === key || opt.key === key
  );
  return option?.optionValue?.value || option?.optionValue?.label || option?.value || '';
};

export const MobileOptimizedWorksheetTable: React.FC<MobileOptimizedWorksheetTableProps> = ({
  worksheets: initialWorksheets,
  error,
  isLoading = false,
  tableTitle = "Your Worksheets",
  entityName = "worksheet",
  entityNamePlural = "worksheets",
  createPath = ERoutes.MANAGE_WORKSHEET_CREATE,
  currentPageBackend,
  totalPagesBackend,
  totalItemsBackend,
  onBackendPageChange,
  onBackendRowsPerPageChange,
  rowsPerPageBackend,
}) => {
  const router = useRouter();
  const [worksheets, setWorksheets] = useState<TWorksheet[]>(initialWorksheets);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedWorksheet, setSelectedWorksheet] = useState<TWorksheet | null>(null);
  const [selectedWorksheetIds, setSelectedWorksheetIds] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Socket connection for real-time updates
  useEffect(() => {
    const socket = getSocket();
    
    socket.on('worksheetStatusUpdate', (data: { worksheetId: string; status: WorksheetGeneratingStatus }) => {
      setWorksheets(prev => prev.map(ws => 
        ws.id === data.worksheetId 
          ? { ...ws, generatingStatus: data.status }
          : ws
      ));
    });

    return () => {
      socket.off('worksheetStatusUpdate');
    };
  }, []);

  // Update worksheets when prop changes
  useEffect(() => {
    setWorksheets(initialWorksheets);
  }, [initialWorksheets]);

  // Filter and sort worksheets
  const filteredAndSortedWorksheets = useMemo(() => {
    const filtered = worksheets.filter(ws =>
      ws.title.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort
    filtered.sort((a, b) => {
      let aValue = getNestedValue(a, sortColumn);
      let bValue = getNestedValue(b, sortColumn);
      
      if (sortColumn === 'createdAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }
      
      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [worksheets, searchTerm, sortColumn, sortDirection]);

  // Handlers
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const handleSelectWorksheet = useCallback((id: string) => {
    setSelectedWorksheetIds(prev =>
      prev.includes(id)
        ? prev.filter(wsId => wsId !== id)
        : [...prev, id]
    );
  }, []);

  const handleSelectAll = useCallback(() => {
    if (isAllSelected) {
      setSelectedWorksheetIds([]);
      setIsAllSelected(false);
    } else {
      setSelectedWorksheetIds(filteredAndSortedWorksheets.map(ws => ws.id));
      setIsAllSelected(true);
    }
  }, [isAllSelected, filteredAndSortedWorksheets]);

  const handleOpenDeleteModal = useCallback((worksheet: TWorksheet) => {
    setSelectedWorksheet(worksheet);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = () => {
    setSelectedWorksheet(null);
    setIsDeleteModalOpen(false);
  };

  const handleDeleteSuccess = (deletedId: string) => {
    setWorksheets(prev => prev.filter(ws => ws.id !== deletedId));
    setSelectedWorksheetIds(prev => prev.filter(id => id !== deletedId));
    handleCloseDeleteModal();
  };

  const handleBulkDelete = () => {
    // Implement bulk delete logic
    console.log('Bulk delete:', selectedWorksheetIds);
  };

  const handlePageChange = (page: number) => {
    if (onBackendPageChange) {
      onBackendPageChange(page);
    } else {
      setCurrentPage(page);
    }
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    if (onBackendRowsPerPageChange) {
      onBackendRowsPerPageChange(event);
    } else {
      setRowsPerPage(parseInt(event.target.value));
      setCurrentPage(1);
    }
  };

  // Handle row click to navigate to detail page
  const handleRowClick = (row: any) => {
    router.push(`${ERoutes.MANAGE_WORKSHEET_REVIEW}&id=${row.original.id}`);
  };

  // Define columns optimized for mobile
  const columns = useMemo<ColumnDef<TWorksheet>[]>(() => [
    {
      id: 'select',
      header: () => (
        <input
          type="checkbox"
          checked={isAllSelected}
          onChange={handleSelectAll}
          className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
          aria-label={`Select all ${entityNamePlural}`}
          disabled={filteredAndSortedWorksheets.length === 0}
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={selectedWorksheetIds.includes(row.original.id)}
          onChange={() => handleSelectWorksheet(row.original.id)}
          onClick={(e) => e.stopPropagation()} // Prevent row click when clicking checkbox
          className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
          aria-label={`Select ${row.original.title}`}
        />
      ),
      size: 40,
    },
    {
      accessorKey: 'title',
      id: 'title',
      header: 'Worksheet',
      cell: ({ row }) => {
        const ws = row.original;
        const grade = getOptionValue(ws.selectedOptions, 'grade');
        const subject = getOptionValue(ws.selectedOptions, 'topic');
        const language = getOptionValue(ws.selectedOptions, 'language');
        
        return (
          <div className="flex items-center gap-3">
            <div className="hidden md:flex w-10 h-10 md:w-12 md:h-12 rounded-lg items-center justify-center bg-blue-100 text-blue-600 flex-shrink-0">
              <FileText className="w-5 h-5 md:w-6 md:h-6" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-medium text-gray-900 text-sm md:text-base truncate">
                {ws.title}
              </div>
              {/* Mobile: Show all details vertically */}
              <div className="text-xs text-gray-500 mt-1 space-y-1 md:hidden">
                {grade && (
                  <div className="flex items-center gap-1">
                    <GraduationCap className="w-3 h-3" />
                    <span>{grade}</span>
                  </div>
                )}
                {subject && (
                  <div className="flex items-center gap-1">
                    <BookOpen className="w-3 h-3" />
                    <span>{subject}</span>
                  </div>
                )}
                {language && (
                  <div className="flex items-center gap-1">
                    <Globe className="w-3 h-3" />
                    <span>{language}</span>
                  </div>
                )}
              </div>
              {/* Desktop: Show all details horizontally */}
              <div className="hidden md:block text-xs text-gray-500 mt-0.5">
                {grade && `${grade} `}
                {subject && `• ${subject} `}
                {language && `• ${language}`}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      id: 'createdAt',
      header: 'Created',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-gray-400 flex-shrink-0" />
          <span className="text-sm text-gray-900">
            {dayjs(row.original.createdAt).format('MMM DD, YYYY')}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'generatingStatus',
      id: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.generatingStatus;
        let badgeType: 'active' | 'inactive' | 'pending' | 'error' = 'inactive';
        if (status === WorksheetGeneratingStatus.GENERATED) {
          badgeType = 'active';
        } else if (status === WorksheetGeneratingStatus.PENDING) {
          badgeType = 'pending';
        } else if (status === WorksheetGeneratingStatus.ERROR) {
          badgeType = 'error';
        }
        return <StatusBadge status={badgeType} />;
      },
    },

    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            onClick={(e) => {
              e.stopPropagation(); // Prevent row click when clicking delete button
              handleOpenDeleteModal(row.original);
            }}
            variant="ghost"
            className="p-2 w-auto h-auto rounded-lg text-red-600 hover:bg-red-50"
            title={`Delete ${entityName}`}
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ], [isAllSelected, handleSelectAll, selectedWorksheetIds, handleSelectWorksheet, handleOpenDeleteModal, filteredAndSortedWorksheets.length, entityName, entityNamePlural, router]);

  // Priority columns for mobile (most important info)
  const priorityColumns = ['title', 'status', 'createdAt'];

  // Pagination calculations
  const currentTotalItems = totalItemsBackend ?? filteredAndSortedWorksheets.length;
  const currentTotalPages = totalPagesBackend ?? Math.ceil(filteredAndSortedWorksheets.length / rowsPerPage);
  const paginatedWorksheets = onBackendPageChange ? worksheets : filteredAndSortedWorksheets.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage);

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center gap-2">
          <AlertCircle size={20} className="text-red-600" />
          <div className="text-red-800 text-sm">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "space-y-4 bg-gray-50 p-4 -m-4",
      selectedWorksheetIds.length > 0 && !isLoading && "pb-32 md:pb-4" // Extra bottom padding on mobile for dock + bulk actions
    )}>
      {/* Header - Only show subtitle, main title comes from page header */}
      <div className="mb-4">
        <p className="text-sm text-gray-600">
          {isLoading ? (
            <div className="skeleton h-4 w-32"></div>
          ) : (
            `${currentTotalItems} ${currentTotalItems === 1 ? entityName : entityNamePlural} found`
          )}
        </p>
      </div>

      {/* Search */}
      <div className="relative bg-white rounded-xl shadow-sm">
        <Search className="w-4 h-4 absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" />
        <input
          type="text"
          placeholder={`Search ${entityNamePlural}...`}
          value={searchTerm}
          onChange={handleSearchChange}
          disabled={isLoading}
          className="pl-11 pr-4 py-3 w-full border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-50 disabled:cursor-not-allowed"
        />
      </div>

      {/* Bulk Actions - Minimal and Sticky for Mobile */}
      {selectedWorksheetIds.length > 0 && !isLoading && (
        <>
          {/* Desktop Bulk Actions */}
          <div className="hidden md:block p-4 border border-blue-200 rounded-xl bg-white shadow-sm">
            <div className="flex justify-between items-center">
              <p className="text-sm text-blue-700 font-medium">
                {selectedWorksheetIds.length} {selectedWorksheetIds.length === 1 ? entityName : entityNamePlural} selected
              </p>
              <div className="flex gap-2">
                <button
                  onClick={() => { setSelectedWorksheetIds([]); setIsAllSelected(false); }}
                  className="px-4 py-2 text-sm text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                >
                  Clear Selection
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="px-4 py-2 text-sm bg-red-500 text-white hover:bg-red-600 rounded-lg flex items-center gap-2 transition-colors"
                >
                  <Trash2 className="w-4 h-4" /> Delete Selected
                </button>
              </div>
            </div>
          </div>

          {/* Mobile Bulk Actions - Above Dock */}
          <div className="md:hidden fixed bottom-14 left-0 right-0 z-50 bg-white border-t max-md:m-0 border-gray-200 shadow-lg">
            <div className="flex items-center justify-between p-3">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-600 text-white text-xs flex items-center justify-center font-medium">
                  {selectedWorksheetIds.length}
                </div>
                <span className="text-sm text-gray-700 font-medium">Selected</span>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => { setSelectedWorksheetIds([]); setIsAllSelected(false); }}
                  className="px-3 py-2 text-xs text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                >
                  Clear
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="px-3 py-2 text-xs bg-red-500 text-white hover:bg-red-600 rounded-md flex items-center gap-1 transition-colors"
                >
                  <Trash2 className="w-3 h-3" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Table */}
      <MobileOptimizedTable
        columns={columns}
        tableData={paginatedWorksheets}
        isLoading={isLoading}
        priorityColumns={priorityColumns}
        mobileBreakpoint="md"
        mobileCardClassName="hover:shadow-lg border border-gray-200 bg-white rounded-xl shadow-sm"
        handleClickTableRow={handleRowClick}
      />

      {/* Pagination */}
      {currentTotalItems > 0 && !isLoading && (
        <MobileOptimizedTablePagination
          currentPage={currentPageBackend || currentPage}
          totalPages={currentTotalPages}
          rowsPerPage={rowsPerPageBackend || rowsPerPage}
          totalItems={currentTotalItems}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
        />
      )}

      {/* Delete Modal */}
      {selectedWorksheet && isDeleteModalOpen && (
        <DeleteWorksheetModal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          worksheet={selectedWorksheet}
          onSuccess={handleDeleteSuccess}
        />
      )}
    </div>
  );
};
